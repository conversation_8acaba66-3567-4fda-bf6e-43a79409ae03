/* Issues List */
.issues-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.issues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.issues-header h1 {
  color: #2c3e50;
  margin: 0;
}

.create-issue-btn {
  background-color: #27ae60;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.create-issue-btn:hover {
  background-color: #229954;
  color: white;
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.filters select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.clear-filters-btn {
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.clear-filters-btn:hover {
  background-color: #7f8c8d;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.issue-main {
  margin-bottom: 1rem;
}

.issue-title {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.2rem;
  display: block;
  margin-bottom: 0.5rem;
}

.issue-title:hover {
  color: #3498db;
}

.issue-description {
  color: #7f8c8d;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.issue-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status.open {
  background-color: #fee;
  color: #e74c3c;
}

.status.closed {
  background-color: #efe;
  color: #27ae60;
}

.created-at, .assignee {
  color: #7f8c8d;
  font-size: 0.875rem;
}

.issue-labels {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.label {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  color: white;
  font-weight: 500;
}

.no-issues {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-issues a {
  color: #3498db;
  text-decoration: none;
}

.no-issues a:hover {
  text-decoration: underline;
}

/* Create Issue */
.create-issue-page, .issue-detail-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
}

.back-btn {
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
}

.back-btn:hover {
  background-color: #7f8c8d;
  color: white;
}

.issue-form {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.form-group input.error {
  border-color: #e74c3c;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.labels-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.label-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.label-preview {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  color: white;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancel-btn {
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #7f8c8d;
}

.submit-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.submit-btn:hover:not(:disabled) {
  background-color: #2980b9;
}

.submit-btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

/* Issue Detail */
.issue-actions {
  display: flex;
  gap: 1rem;
}

.status-btn {
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.status-btn.open {
  background-color: #e74c3c;
  color: white;
}

.status-btn.closed {
  background-color: #27ae60;
  color: white;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.issue-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.issue-header h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.issue-description {
  margin: 2rem 0;
}

.issue-description h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.description-content {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #3498db;
  white-space: pre-wrap;
}

.comments-section {
  margin-top: 3rem;
}

.comments-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.comment-form {
  margin-bottom: 2rem;
}

.comment-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 1rem;
  resize: vertical;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment {
  border: 1px solid #ecf0f1;
  border-radius: 4px;
  padding: 1rem;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.comment-author {
  font-weight: 500;
  color: #2c3e50;
}

.comment-date {
  color: #7f8c8d;
  font-size: 0.875rem;
}

.comment-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 0.875rem;
}

.edit-btn:hover {
  text-decoration: underline;
}

.comment-content {
  white-space: pre-wrap;
  line-height: 1.5;
}

.edit-comment-form textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.edit-actions {
  display: flex;
  gap: 0.5rem;
}

.save-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.save-btn:hover {
  background-color: #229954;
}

.priority-low { color: #95a5a6; }
.priority-medium { color: #f39c12; }
.priority-high { color: #e67e22; }
.priority-urgent { color: #e74c3c; }
