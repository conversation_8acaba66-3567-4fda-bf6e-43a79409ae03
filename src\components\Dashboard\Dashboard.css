.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  color: #2c3e50;
  margin: 0;
}

.create-issue-btn {
  background-color: #27ae60;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.create-issue-btn:hover {
  background-color: #229954;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 1rem 0;
  color: #7f8c8d;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.stat-number.open {
  color: #e74c3c;
}

.stat-number.closed {
  color: #27ae60;
}

.recent-issues {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recent-issues h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-item {
  padding: 1rem;
  border: 1px solid #ecf0f1;
  border-radius: 4px;
  transition: border-color 0.2s;
}

.issue-item:hover {
  border-color: #bdc3c7;
}

.issue-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.issue-title {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
}

.issue-title:hover {
  color: #3498db;
}

.issue-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.open {
  background-color: #fee;
  color: #e74c3c;
}

.status.closed {
  background-color: #efe;
  color: #27ae60;
}

.created-at {
  color: #7f8c8d;
  font-size: 0.875rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
}
